import { ref, computed } from 'vue';

/**
 * 音频管理器 - 处理浏览器自动播放策略和用户交互
 */
export function useAudioManager() {
  const hasUserInteracted = ref(false);
  const isAudioEnabled = ref(false);
  const audioContext = ref<AudioContext | null>(null);
  
  // 检测浏览器是否支持自动播放
  const canAutoplay = computed(() => {
    // 检查是否在移动设备上
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    return !isMobile && hasUserInteracted.value;
  });

  // 初始化音频上下文
  function initAudioContext() {
    if (!audioContext.value) {
      try {
        audioContext.value = new (window.AudioContext || (window as any).webkitAudioContext)();
      } catch (error) {
        console.warn('AudioContext not supported:', error);
      }
    }
  }

  // 恢复音频上下文（需要用户交互）
  async function resumeAudioContext() {
    if (audioContext.value && audioContext.value.state === 'suspended') {
      try {
        await audioContext.value.resume();
        console.log('AudioContext resumed');
      } catch (error) {
        console.warn('Failed to resume AudioContext:', error);
      }
    }
  }

  // 处理用户交互
  async function handleUserInteraction() {
    if (!hasUserInteracted.value) {
      hasUserInteracted.value = true;
      isAudioEnabled.value = true;
      
      // 初始化并恢复音频上下文
      initAudioContext();
      await resumeAudioContext();
      
      console.log('User interaction detected, audio enabled');
    }
  }

  // 尝试启用音频（需要在用户交互后调用）
  async function enableAudio(videoElement?: HTMLVideoElement) {
    if (!hasUserInteracted.value) {
      console.warn('Cannot enable audio without user interaction');
      return false;
    }

    try {
      if (videoElement) {
        videoElement.muted = false;
        // 如果视频正在播放，重新播放以应用音频设置
        if (!videoElement.paused) {
          const currentTime = videoElement.currentTime;
          await videoElement.play();
          videoElement.currentTime = currentTime;
        }
      }
      
      isAudioEnabled.value = true;
      await resumeAudioContext();
      return true;
    } catch (error) {
      console.error('Failed to enable audio:', error);
      return false;
    }
  }

  // 禁用音频
  function disableAudio(videoElement?: HTMLVideoElement) {
    if (videoElement) {
      videoElement.muted = true;
    }
    isAudioEnabled.value = false;
  }

  // 智能播放视频（处理自动播放策略）
  async function smartPlayVideo(videoElement: HTMLVideoElement, options: {
    preferUnmuted?: boolean;
    fallbackToMuted?: boolean;
  } = {}) {
    const { preferUnmuted = true, fallbackToMuted = true } = options;
    
    try {
      // 如果用户已经交互过且偏好有声播放，尝试有声播放
      if (hasUserInteracted.value && preferUnmuted) {
        videoElement.muted = false;
        await videoElement.play();
        isAudioEnabled.value = true;
        console.log('Video playing with sound');
        return { success: true, muted: false };
      } else {
        // 否则静音播放
        videoElement.muted = true;
        await videoElement.play();
        isAudioEnabled.value = false;
        console.log('Video playing muted');
        return { success: true, muted: true };
      }
    } catch (error) {
      console.warn('Video play failed:', error);
      
      // 如果播放失败且允许回退到静音，尝试静音播放
      if (fallbackToMuted && !videoElement.muted) {
        try {
          videoElement.muted = true;
          await videoElement.play();
          isAudioEnabled.value = false;
          console.log('Fallback to muted play successful');
          return { success: true, muted: true };
        } catch (mutedError) {
          console.error('Even muted play failed:', mutedError);
          return { success: false, muted: true, error: mutedError };
        }
      }
      
      return { success: false, muted: videoElement.muted, error };
    }
  }

  // 设置全局事件监听器
  function setupGlobalListeners() {
    const events = ['touchstart', 'touchend', 'mousedown', 'keydown', 'click'];
    
    const handleInteraction = () => {
      handleUserInteraction();
      // 移除监听器，因为只需要检测第一次交互
      events.forEach(event => {
        document.removeEventListener(event, handleInteraction);
      });
    };

    events.forEach(event => {
      document.addEventListener(event, handleInteraction, { once: true, passive: true });
    });
  }

  // 清理资源
  function cleanup() {
    if (audioContext.value) {
      audioContext.value.close();
      audioContext.value = null;
    }
  }

  return {
    hasUserInteracted: readonly(hasUserInteracted),
    isAudioEnabled: readonly(isAudioEnabled),
    canAutoplay,
    handleUserInteraction,
    enableAudio,
    disableAudio,
    smartPlayVideo,
    setupGlobalListeners,
    cleanup,
  };
}

// 全局音频管理器实例
let globalAudioManager: ReturnType<typeof useAudioManager> | null = null;

export function useGlobalAudioManager() {
  if (!globalAudioManager) {
    globalAudioManager = useAudioManager();
    globalAudioManager.setupGlobalListeners();
  }
  return globalAudioManager;
}
