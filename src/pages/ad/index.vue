<script setup lang="ts">
import AdvertiserTags from './components/AdvertiserTags.vue'; // @unocss-included
import { useVideoAdProvider } from './useVideoAd';
import 'video.js/dist/video-js.css';

const videoElement = ref<HTMLVideoElement | null>(null);
const {
  videoAdUrl,
  objectFit,
  isMuted,
  toggleMute,
} = useVideoAdProvider(videoElement)!;
</script>

<template>
  <div class="mx-auto max-w-40 w-full">
    <div class="relative h-100vh flex-between flex-col overflow-hidden overflow-hidden bg-black">
      <div class="relative h-100vh w-100% flex-center">
        <video
          v-if="videoAdUrl"
          id="video-js"
          ref="videoElement"
          class="video-js vjs-tech vjs-default-skin video-ad h-100% w-100%"
          preload="auto"
          autoplay
          loop
          :muted="isMuted"
          x5-playsinline
          webkit-playsinline
          playsinline
          x5-video-player-type="h5"
          data-setup="{}"
        >
          <source :src="videoAdUrl" type="application/x-mpegURL">
          <source :src="videoAdUrl" type="video/mp4">
          <p class="vjs-no-js text-center">MP4 format is not supported</p>
        </video>

        <!-- 音频控制按钮 - 右上角 -->
        <div class="absolute top-4 right-4 z-20">
          <div class="audio-control-container">
            <button
              @click="toggleMute"
              class="audio-control-btn"
              :class="{ 'muted': isMuted, 'unmuted': !isMuted }"
              :title="isMuted ? '点击开启声音' : '点击静音'"
            >
              <!-- 有声音图标 -->
              <svg
                v-if="!isMuted"
                class="audio-icon"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
              </svg>

              <!-- 静音图标 -->
              <svg
                v-else
                class="audio-icon"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"/>
              </svg>
            </button>

            <!-- 音频波形动画 -->
            <div v-if="!isMuted" class="audio-waves">
              <div class="wave wave-1"></div>
              <div class="wave wave-2"></div>
              <div class="wave wave-3"></div>
            </div>

            <!-- 状态文字 -->
            <div class="audio-status-text">
              {{ isMuted ? '静音' : '有声' }}
            </div>
          </div>
        </div>
      </div>

      <div class="absolute bottom-4.5 z-1 w-full">
        <AdvertiserTags advertisers="g9" />
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.ant-cover__Modal-video-ad.ant-cover__Modal-transparent {
  .ant-modal-body {
    padding: 0 !important;
  }
  .ant-modal {
    height: auto;
    width: 100%;
    --uno: 'max-w-md';
  }
  .ant-modal-content {
    padding: 0 !important;
  }
}
.video-ad {
  .vjs-error-display.vjs-modal-dialog {
    .vjs-modal-dialog-content {
      display: grid;
      place-content: center;
      z-index: 0;
    }
  }
}
</style>

<style scoped lang="scss">
.vjs-tech {
  object-fit: v-bind('objectFit');
}

.audio-control-container {
  @apply flex flex-col items-center gap-2;
}

.audio-control-btn {
  @apply w-14 h-14 rounded-full flex-center transition-all duration-300 ease-in-out;
  @apply border-2 backdrop-blur-sm cursor-pointer relative;
  @apply hover:scale-110 active:scale-95;

  &.unmuted {
    @apply bg-green-500/90 border-green-400 text-white;
    @apply hover:bg-green-600/95 hover:border-green-300;
    box-shadow: 0 4px 16px rgba(34, 197, 94, 0.4);
  }

  &.muted {
    @apply bg-red-500/90 border-red-400 text-white;
    @apply hover:bg-red-600/95 hover:border-red-300;
    box-shadow: 0 4px 16px rgba(239, 68, 68, 0.4);
  }
}

.audio-icon {
  @apply w-7 h-7 transition-transform duration-200;
}

.audio-control-btn:hover .audio-icon {
  @apply scale-110;
}

.audio-waves {
  @apply flex items-center gap-1 absolute -right-16 top-1/2 transform -translate-y-1/2;
}

.wave {
  @apply w-1 bg-green-400 rounded-full;
  animation: wave-animation 1.5s ease-in-out infinite;
}

.wave-1 {
  height: 8px;
  animation-delay: 0s;
}

.wave-2 {
  height: 12px;
  animation-delay: 0.2s;
}

.wave-3 {
  height: 6px;
  animation-delay: 0.4s;
}

.audio-status-text {
  @apply text-xs font-medium px-2 py-1 rounded-full backdrop-blur-sm;
  @apply bg-black/50 text-white border border-white/20;
  @apply transition-all duration-300;
}

/* 添加脉冲动画效果 */
.audio-control-btn.unmuted {
  animation: pulse-green 3s infinite;
}

.audio-control-btn.muted {
  animation: pulse-red 3s infinite;
}

@keyframes pulse-green {
  0%, 100% {
    box-shadow: 0 4px 16px rgba(34, 197, 94, 0.4);
  }
  50% {
    box-shadow: 0 4px 24px rgba(34, 197, 94, 0.6), 0 0 0 4px rgba(34, 197, 94, 0.1);
  }
}

@keyframes pulse-red {
  0%, 100% {
    box-shadow: 0 4px 16px rgba(239, 68, 68, 0.4);
  }
  50% {
    box-shadow: 0 4px 24px rgba(239, 68, 68, 0.6), 0 0 0 4px rgba(239, 68, 68, 0.1);
  }
}

@keyframes wave-animation {
  0%, 100% {
    transform: scaleY(1);
  }
  50% {
    transform: scaleY(1.8);
  }
}

/* 响应式设计 */
@media (max-width: 640px) {
  .audio-control-container {
    @apply scale-90;
  }

  .audio-waves {
    @apply -right-12;
  }
}
</style>
