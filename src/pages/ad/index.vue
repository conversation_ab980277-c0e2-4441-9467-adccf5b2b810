<script setup lang="ts">
import AdvertiserTags from './components/AdvertiserTags.vue'; // @unocss-included
import { useVideoAdProvider } from './useVideoAd';
import 'video.js/dist/video-js.css';

const videoElement = ref<HTMLVideoElement | null>(null);
const {
  videoAdUrl,
  objectFit,
  isMuted,
} = useVideoAdProvider(videoElement)!;

onMounted(() => {
  // Unmute video on mount to enable sound
  if (videoElement.value) {
    videoElement.value.muted = false;
    isMuted.value = false;
  }
});
</script>

<template>
  <div class="mx-auto max-w-40 w-full">
    <div class="relative h-100vh flex-between flex-col overflow-hidden overflow-hidden bg-black">
      <div class="relative h-100vh w-100% flex-center">
        <video
          v-if="videoAdUrl"
          id="video-js"
          ref="videoElement"
          class="video-js vjs-tech vjs-default-skin video-ad h-100% w-100%"
          preload="auto"
          autoplay
          loop
          :muted="isMuted"
          x5-playsinline
          webkit-playsinline
          playsinline
          x5-video-player-type="h5"
          data-setup="{}"
        >
          <source :src="videoAdUrl" type="application/x-mpegURL">
          <source :src="videoAdUrl" type="video/mp4">
          <p class="vjs-no-js text-center">MP4 format is not supported</p>
        </video>
      </div>

      <div class="absolute bottom-4.5 z-1 w-full">
        <AdvertiserTags advertisers="g9" />
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.ant-cover__Modal-video-ad.ant-cover__Modal-transparent {
  .ant-modal-body {
    padding: 0 !important;
  }
  .ant-modal {
    height: auto;
    width: 100%;
    --uno: 'max-w-md';
  }
  .ant-modal-content {
    padding: 0 !important;
  }
}
.video-ad {
  .vjs-error-display.vjs-modal-dialog {
    .vjs-modal-dialog-content {
      display: grid;
      place-content: center;
      z-index: 0;
    }
  }
}
</style>

<style scoped lang="scss">
.vjs-tech {
  object-fit: v-bind('objectFit');
}
</style>
