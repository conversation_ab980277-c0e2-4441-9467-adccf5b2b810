<script setup lang="ts">
import AdvertiserTags from './components/AdvertiserTags.vue'; // @unocss-included
import { useVideoAdProvider } from './useVideoAd';
import 'video.js/dist/video-js.css';

const videoElement = ref<HTMLVideoElement | null>(null);
const {
  videoAdUrl,
  objectFit,
  isMuted,
  hasUserInteracted,
  isAudioEnabled,
  openSound,
  tryAutoUnmute,
} = useVideoAdProvider(videoElement)!;

// 处理视频点击事件，立即取消静音
function handleVideoClick() {
  if (isMuted.value) {
    openSound();
  }
}

onMounted(() => {
  // 尝试在组件挂载后自动取消静音（如果用户已经交互过）
  nextTick(() => {
    tryAutoUnmute();
  });
});
</script>

<template>
  <div class="mx-auto max-w-40 w-full">
    <div class="relative h-100vh flex-between flex-col overflow-hidden overflow-hidden bg-black">
      <div class="relative h-100vh w-100% flex-center" @click="handleVideoClick">
        <video
          v-if="videoAdUrl"
          id="video-js"
          ref="videoElement"
          class="video-js vjs-tech vjs-default-skin video-ad h-100% w-100%"
          preload="auto"
          autoplay
          loop
          :muted="isMuted"
          x5-playsinline
          webkit-playsinline
          playsinline
          x5-video-player-type="h5"
          data-setup="{}"
        >
          <source :src="videoAdUrl" type="application/x-mpegURL">
          <source :src="videoAdUrl" type="video/mp4">
          <p class="vjs-no-js text-center">MP4 format is not supported</p>
        </video>

        <!-- 静音提示 -->
        <div
          v-if="isMuted && !hasUserInteracted"
          class="absolute inset-0 flex-center bg-black/30 backdrop-blur-sm z-10 pointer-events-none"
        >
          <div class="bg-white/90 rounded-lg px-6 py-4 text-center shadow-lg animate-pulse">
            <div class="text-2xl mb-2">🔊</div>
            <div class="text-sm text-gray-800 font-medium">点击屏幕开启声音</div>
            <div class="text-xs text-gray-600 mt-1">Tap to unmute</div>
          </div>
        </div>

        <!-- 音频状态指示器 -->
        <div
          v-if="hasUserInteracted"
          class="absolute top-4 right-4 z-20 bg-black/50 rounded-full p-2 text-white text-sm"
        >
          <div v-if="isAudioEnabled && !isMuted" class="flex items-center gap-1">
            <span class="text-green-400">🔊</span>
            <span class="text-xs">音频已开启</span>
          </div>
          <div v-else class="flex items-center gap-1">
            <span class="text-red-400">🔇</span>
            <span class="text-xs">静音</span>
          </div>
        </div>
      </div>

      <div class="absolute bottom-4.5 z-1 w-full">
        <AdvertiserTags advertisers="g9" />
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.ant-cover__Modal-video-ad.ant-cover__Modal-transparent {
  .ant-modal-body {
    padding: 0 !important;
  }
  .ant-modal {
    height: auto;
    width: 100%;
    --uno: 'max-w-md';
  }
  .ant-modal-content {
    padding: 0 !important;
  }
}
.video-ad {
  .vjs-error-display.vjs-modal-dialog {
    .vjs-modal-dialog-content {
      display: grid;
      place-content: center;
      z-index: 0;
    }
  }
}
</style>

<style scoped lang="scss">
.vjs-tech {
  object-fit: v-bind('objectFit');
}
</style>
