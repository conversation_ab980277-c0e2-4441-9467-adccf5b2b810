import type Player from 'video.js/dist/types/player';
import type { IVideoInfo, IVideoPlayState } from './utils';
import { createInjectionState, useToggle } from '@peng_kai/kit/libs/vueuse';
import videojs from 'video.js';

export const [useVideoAdProvider, useVideoAd] = createInjectionState((videoElement: Ref<HTMLVideoElement> | Ref<null>) => {
  // const { videoId, isPlaybackEnd } = useAd();
  // const appStore = useAppStore();
  const isMuted = ref(false); // 尝试默认不静音
  // const videoInfoQry = useQuery({
  //   queryKey: [apis.apiRewardedVideoCreate.id, videoId],
  //   queryFn: () => apis.apiRewardedVideoCreate(undefined),
  // });

  // 视频播放状态
  const state = reactive<IVideoPlayState>({
    pend: false,
    pause: false,
    stopIt: false,
    isLoadeddata: false,
    isLoading: false,
    isError: false,
    delayCount: 0,
    isUnlock: false,
  });

  const player = shallowRef<Player | undefined>();
  const [playing] = useToggle(false);
  // 视频广告信息
  // const videoAdInfo = computed(() => videoInfoQry.data.value);

  /*
 * m3u8 测试视频
 * http://playertest.longtailvideo.com/adaptive/bipbop/gear4/prog_index.m3u8
 * https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8
 * */
  // 视频广告url
  const videoAdUrl = computed(() => {
    // 生成1到6的随机整数
    const randomNum = Math.floor(Math.random() * 13) + 1;
    return `https://cdn.g9aaa.com/adv/${randomNum}/${randomNum}.m3u8`;
  });
  // 视频信息
  const _video = reactive<IVideoInfo>({
    videoWidth: 0,
    videoHeight: 0,
    duration: 0,
  });
  const aspectRatio = computed(() => _video.videoWidth / _video.videoHeight);
  const objectFit = computed(() => aspectRatio.value < 0.5 ? 'cover' : 'contain');
  /*
  * 加载图标显示
  * 1、还在首帧加载中
  * 2、视频加载错误
  * 3、首帧加载完毕但还未播放
  * */
  const isLoading = computed(() => state.isLoadeddata && !state.isError);
  // 视频首帧加载完成
  async function handleLoadeddata() {
    state.isLoadeddata = false;
    videoElement.value?.play();
    if (toValue(videoElement)) {
      _video.videoWidth = player.value?.videoWidth() ?? 0;
      _video.videoHeight = player.value?.videoHeight() ?? 0;
      _video.duration = Math.floor(player.value!.duration() || 0);

      // 尝试直接播放有声视频
      try {
        state.isLoadeddata = false;
        if (player.value) {
          player.value.muted(false); // 强制设置为非静音
          isMuted.value = false;
        }
        await videoElement.value?.play();
        console.log('Video playing with sound');
      } catch (error) {
        console.error('Video play failed:', error);
        // 如果有声播放失败，回退到静音播放
        if (player.value) {
          player.value.muted(true);
          isMuted.value = true;
          try {
            await player.value.play();
            console.log('Fallback to muted play');
          } catch (mutedPlayError) {
            console.error('Even muted play failed:', mutedPlayError);
          }
        }
      }
    }
  }

  // 视频开始播放
  async function handleVideoPlay() {
    try {
      playing.value = true;
      state.isLoading = false;
      //   视频开始播放后操作
    }
    catch (e) {
    }
  }

  // 视频加载完成版
  function handleEnded() {
    state.pend = true;
    state.stopIt = true;
    //   视频加载完毕后的操作
  }

  // 浏览器加载中
  function handleVideoLoadstart() {
    state.isLoadeddata = true;
  }

  // 视频暂停
  function handleVideoPause() {
    playing.value = false;
  }

  function handleVideoWaiting() {
    state.delayCount++;
    state.isLoading = true;
    playing.value = false;
  }

  function handleVisibilityChange() {
    if (state.pend)
      return;
    if (document.hidden) {
      player.value?.pause();
    }
    else {
      player.value?.play();
    }
  }

  function openSound() {
    isMuted.value = false;
    !state.pend && player.value?.muted(false);
  }

  function handleVideoError() {
    state.isError = true;
    // const err = player.value?.error();

    /*
      1 - MEDIA_ERR_ABORTED：用户中断了视频加载（例如，用户点击了“停止”）。
      2 - MEDIA_ERR_NETWORK：由于网络错误，视频无法加载。这通常意味着资源不可用或网络连接问题。
      3 - MEDIA_ERR_DECODE：解码错误，视频无法播放，可能是由于格式不支持或文件损坏。
      4 - MEDIA_ERR_SRC_NOT_SUPPORTED：文件类型不被支持，可能是文件格式不正确或未被实现的 codecs。
    */
  }

  document.addEventListener('visibilitychange', handleVisibilityChange);
  document.addEventListener('click', openSound);

  onBeforeUnmount(() => {
    player.value?.dispose();
    player.value = undefined;
  });

  watch(videoElement, (e) => {
    if (e) {
      const options = {
        controls: false,
        autoplay: false,
        preload: 'auto',
        muted: false,
      };
      player.value = videojs(videoElement.value!, options);
      player.value.on('playing', handleVideoPlay);
      player.value.on('waiting', handleVideoWaiting);
      player.value.on('pause', handleVideoPause);
      player.value.on('ended', handleEnded);
      player.value.on('loadeddata', handleLoadeddata);
      player.value.on('loadstart', handleVideoLoadstart);
      player.value.on('error', handleVideoError);
    }
  });
  return {
    videoAdUrl,
    videoInfo: _video,
    objectFit,
    isLoading,
    state,
    playing,
    isMuted,
    onPlay: () => player.value?.play(),
    onPause: () => player.value?.pause(),
  };
}, {
  injectionKey: 'videoAD',
});
