<script setup lang="ts">
import { useGlobalAudioManager } from '~/composables/useAudioManager';

const audioManager = useGlobalAudioManager();
const videoRef = ref<HTMLVideoElement | null>(null);
const isPlaying = ref(false);
const currentStrategy = ref<'auto' | 'manual' | 'smart'>('smart');

// 不同的播放策略
const strategies = {
  auto: {
    name: '自动播放（可能失败）',
    description: '直接尝试有声播放，可能被浏览器阻止',
  },
  manual: {
    name: '手动交互后播放',
    description: '等待用户交互后再播放有声视频',
  },
  smart: {
    name: '智能播放策略（推荐）',
    description: '先静音播放，用户交互后自动开启声音',
  },
};

// 自动播放策略
async function autoPlay() {
  if (!videoRef.value) return;
  
  try {
    videoRef.value.muted = false;
    await videoRef.value.play();
    isPlaying.value = true;
    console.log('Auto play with sound successful');
  } catch (error) {
    console.error('Auto play failed:', error);
    alert('自动播放失败：' + error.message);
  }
}

// 手动播放策略
async function manualPlay() {
  if (!videoRef.value) return;
  
  if (!audioManager.hasUserInteracted.value) {
    alert('请先与页面交互（点击任意位置）');
    return;
  }
  
  try {
    videoRef.value.muted = false;
    await videoRef.value.play();
    isPlaying.value = true;
    console.log('Manual play successful');
  } catch (error) {
    console.error('Manual play failed:', error);
    alert('手动播放失败：' + error.message);
  }
}

// 智能播放策略
async function smartPlay() {
  if (!videoRef.value) return;
  
  const result = await audioManager.smartPlayVideo(videoRef.value, {
    preferUnmuted: true,
    fallbackToMuted: true,
  });
  
  if (result.success) {
    isPlaying.value = true;
    if (result.muted) {
      console.log('Smart play: started muted, will unmute after user interaction');
    } else {
      console.log('Smart play: started with sound');
    }
  } else {
    console.error('Smart play failed:', result.error);
    alert('智能播放失败：' + result.error?.message);
  }
}

// 停止播放
function stopPlay() {
  if (videoRef.value) {
    videoRef.value.pause();
    videoRef.value.currentTime = 0;
    isPlaying.value = false;
  }
}

// 切换静音
async function toggleMute() {
  if (!videoRef.value) return;
  
  if (videoRef.value.muted) {
    const success = await audioManager.enableAudio(videoRef.value);
    if (!success) {
      alert('无法开启声音，请先与页面交互');
    }
  } else {
    audioManager.disableAudio(videoRef.value);
  }
}

// 执行选定的策略
async function executeStrategy() {
  stopPlay();
  
  switch (currentStrategy.value) {
    case 'auto':
      await autoPlay();
      break;
    case 'manual':
      await manualPlay();
      break;
    case 'smart':
      await smartPlay();
      break;
  }
}

// 监听用户交互
watch(audioManager.hasUserInteracted, (interacted) => {
  if (interacted && videoRef.value?.muted && isPlaying.value) {
    // 用户交互后自动尝试开启声音
    audioManager.enableAudio(videoRef.value);
  }
});
</script>

<template>
  <div class="max-w-4xl mx-auto p-6 space-y-6">
    <div class="text-center">
      <h1 class="text-3xl font-bold mb-4">浏览器视频静音问题解决方案演示</h1>
      <p class="text-gray-600">
        演示不同的视频播放策略，解决现代浏览器的自动播放限制
      </p>
    </div>

    <!-- 状态指示器 -->
    <div class="bg-gray-100 rounded-lg p-4">
      <h3 class="font-semibold mb-2">当前状态</h3>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        <div class="flex items-center gap-2">
          <span class="w-3 h-3 rounded-full" :class="audioManager.hasUserInteracted.value ? 'bg-green-500' : 'bg-red-500'"></span>
          <span>用户交互: {{ audioManager.hasUserInteracted.value ? '是' : '否' }}</span>
        </div>
        <div class="flex items-center gap-2">
          <span class="w-3 h-3 rounded-full" :class="audioManager.isAudioEnabled.value ? 'bg-green-500' : 'bg-red-500'"></span>
          <span>音频启用: {{ audioManager.isAudioEnabled.value ? '是' : '否' }}</span>
        </div>
        <div class="flex items-center gap-2">
          <span class="w-3 h-3 rounded-full" :class="isPlaying ? 'bg-green-500' : 'bg-gray-500'"></span>
          <span>播放状态: {{ isPlaying ? '播放中' : '已停止' }}</span>
        </div>
        <div class="flex items-center gap-2">
          <span class="w-3 h-3 rounded-full" :class="videoRef?.muted === false ? 'bg-green-500' : 'bg-red-500'"></span>
          <span>音频: {{ videoRef?.muted === false ? '开启' : '静音' }}</span>
        </div>
      </div>
    </div>

    <!-- 策略选择 -->
    <div class="bg-white border rounded-lg p-4">
      <h3 class="font-semibold mb-4">选择播放策略</h3>
      <div class="space-y-3">
        <div 
          v-for="(strategy, key) in strategies" 
          :key="key"
          class="flex items-start gap-3 p-3 border rounded cursor-pointer hover:bg-gray-50"
          :class="currentStrategy === key ? 'border-blue-500 bg-blue-50' : 'border-gray-200'"
          @click="currentStrategy = key as any"
        >
          <input 
            type="radio" 
            :value="key" 
            v-model="currentStrategy" 
            class="mt-1"
          >
          <div>
            <div class="font-medium">{{ strategy.name }}</div>
            <div class="text-sm text-gray-600">{{ strategy.description }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 视频播放器 -->
    <div class="bg-black rounded-lg overflow-hidden">
      <video
        ref="videoRef"
        class="w-full h-64 object-cover"
        preload="auto"
        loop
        controls
      >
        <source src="https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8" type="application/x-mpegURL">
        <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4">
        您的浏览器不支持视频播放
      </video>
    </div>

    <!-- 控制按钮 -->
    <div class="flex flex-wrap gap-3 justify-center">
      <button 
        @click="executeStrategy"
        class="px-6 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
      >
        执行选定策略
      </button>
      <button 
        @click="stopPlay"
        class="px-6 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
      >
        停止播放
      </button>
      <button 
        @click="toggleMute"
        class="px-6 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
      >
        {{ videoRef?.muted ? '开启声音' : '静音' }}
      </button>
    </div>

    <!-- 说明文档 -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <h3 class="font-semibold mb-2 text-yellow-800">💡 使用说明</h3>
      <ul class="text-sm text-yellow-700 space-y-1">
        <li>• <strong>智能播放策略</strong>是推荐的解决方案，它会先静音播放确保视频能够开始，然后在用户交互后自动开启声音</li>
        <li>• 现代浏览器（Chrome 66+, Safari 11+）默认阻止自动播放有声视频</li>
        <li>• 用户必须先与页面交互（点击、触摸等）才能播放有声音的媒体</li>
        <li>• 移动设备上的限制更加严格</li>
      </ul>
    </div>
  </div>
</template>

<style scoped>
/* 添加一些动画效果 */
.transition-colors {
  transition: background-color 0.2s ease;
}
</style>
